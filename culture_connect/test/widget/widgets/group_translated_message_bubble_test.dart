import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';
import 'package:culture_connect/widgets/messaging/group_translated_message_bubble.dart';
import 'package:culture_connect/theme/app_theme.dart';

// Manual mock classes
class MockAuthNotifier extends StateNotifier<AsyncValue<dynamic>> {
  MockAuthNotifier() : super(const AsyncValue.data(null));
}

class MockGroupTranslationNotifier extends StateNotifier<AsyncValue<void>> {
  MockGroupTranslationNotifier() : super(const AsyncValue.data(null));
}

void main() {
  late ProviderContainer container;

  // Sample data for testing
  final sampleGroupId = 'group-123';
  final sampleUserId = 'user-456';
  final sampleMessageId = 'message-789';

  final sampleMessage = MessageModel(
    id: sampleMessageId,
    chatId: sampleGroupId,
    senderId: 'sender-123',
    recipientId: '',
    text: 'Hello, world!',
    timestamp: DateTime.now(),
    status: MessageStatus.sent,
    type: MessageType.text,
    originalLanguage: 'en',
  );

  final sampleTranslationMetadata = MessageTranslationMetadata(
    messageId: sampleMessageId,
    originalText: 'Hello, world!',
    translatedText: 'Bonjour, monde!',
    sourceLanguage: 'en',
    targetLanguage: 'fr',
    confidence: 0.95,
    culturalContext: null,
    slangIdiom: null,
    pronunciation: null,
  );

  final sampleParticipantPreference = ParticipantLanguagePreference(
    userId: sampleUserId,
    displayName: 'Test User',
    preferredLanguage: LanguageModel(code: 'fr', name: 'French'),
    autoTranslate: true,
    showOriginalText: false,
  );

  final sampleGroupSettings = GroupTranslationSettings(
    groupId: sampleGroupId,
    participantPreferences: {
      sampleUserId: sampleParticipantPreference,
    },
    enableRealTimeTranslation: true,
  );

  setUp(() {
    // Create a ProviderContainer with overrides
    container = ProviderContainer(
      overrides: [
        // Override auth provider
        currentUserProvider.overrideWith((ref) => AsyncValue.data(UserModel(
              id: sampleUserId,
              firstName: 'Test',
              lastName: 'User',
              email: '<EMAIL>',
              phoneNumber: '+**********',
              userType: 'tourist',
              isVerified: true,
              verificationLevel: 1,
              status: 'active',
              createdAt: DateTime.now().toIso8601String(),
              updatedAt: DateTime.now().toIso8601String(),
              lastLogin: DateTime.now().toIso8601String(),
              emailVerified: true,
            ))),

        // Override group translation providers
        groupTranslationSettingsProvider(sampleGroupId)
            .overrideWith((ref) => Future.value(sampleGroupSettings)),

        translationMetadataForUserProvider((
          message: sampleMessage,
          userId: sampleUserId,
          groupId: sampleGroupId,
        )).overrideWith((ref) => Future.value(sampleTranslationMetadata)),

        translatedTextForUserProvider((
          message: sampleMessage,
          userId: sampleUserId,
          groupId: sampleGroupId,
        )).overrideWith((ref) => Future.value('Bonjour, monde!')),

        participantLanguagePreferenceProvider((
          groupId: sampleGroupId,
          userId: sampleUserId,
        )).overrideWith((ref) => Future.value(sampleParticipantPreference)),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('GroupTranslatedMessageBubble Widget Tests', () {
    testWidgets(
        'should display translated message when translation is available',
        (WidgetTester tester) async {
      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupTranslatedMessageBubble(
                message: sampleMessage,
                groupId: sampleGroupId,
                senderName: 'Sender Name',
                isMe: false,
              ),
            ),
          ),
        ),
      );

      // Initial build will show loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Should show translated text
      expect(find.text('Bonjour, monde!'), findsOneWidget);

      // Should show sender name
      expect(find.text('Sender Name'), findsOneWidget);

      // Should show translation indicator
      expect(find.text('Translated from English'), findsOneWidget);
    });

    testWidgets(
        'should display original message when translation is not available',
        (WidgetTester tester) async {
      // Create a container with overrides for no translation
      final noTranslationContainer = ProviderContainer(
        overrides: [
          // Override auth provider
          currentUserProvider.overrideWith((ref) => AsyncValue.data(
              UserModel(uid: sampleUserId, email: '<EMAIL>'))),

          // Override group translation providers
          groupTranslationSettingsProvider(sampleGroupId)
              .overrideWith((ref) => Future.value(sampleGroupSettings)),

          translationMetadataForUserProvider((
            message: sampleMessage,
            userId: sampleUserId,
            groupId: sampleGroupId,
          )).overrideWith((ref) => Future.value(null)),

          translatedTextForUserProvider((
            message: sampleMessage,
            userId: sampleUserId,
            groupId: sampleGroupId,
          )).overrideWith((ref) => Future.value(sampleMessage.text)),

          participantLanguagePreferenceProvider((
            groupId: sampleGroupId,
            userId: sampleUserId,
          )).overrideWith((ref) => Future.value(sampleParticipantPreference)),
        ],
      );

      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: noTranslationContainer,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupTranslatedMessageBubble(
                message: sampleMessage,
                groupId: sampleGroupId,
                senderName: 'Sender Name',
                isMe: false,
              ),
            ),
          ),
        ),
      );

      // Initial build will show loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Should show original text
      expect(find.text('Hello, world!'), findsOneWidget);

      // Should not show translation indicator
      expect(find.text('Translated from English'), findsNothing);
    });

    testWidgets('should display message differently when sent by current user',
        (WidgetTester tester) async {
      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupTranslatedMessageBubble(
                message: sampleMessage,
                groupId: sampleGroupId,
                senderName: 'Sender Name',
                isMe: true,
              ),
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Should show translated text
      expect(find.text('Bonjour, monde!'), findsOneWidget);

      // Should not show sender name for own messages
      expect(find.text('Sender Name'), findsNothing);

      // Message should be aligned to the right
      final bubbleContainer = tester.widget<Row>(find.byType(Row).first);
      expect(bubbleContainer.mainAxisAlignment, MainAxisAlignment.end);
    });

    testWidgets('should show original text when showOriginalText is true',
        (WidgetTester tester) async {
      // Create a container with overrides for showing original text
      final showOriginalContainer = ProviderContainer(
        overrides: [
          // Override auth provider
          currentUserProvider.overrideWith((ref) => AsyncValue.data(
              UserModel(uid: sampleUserId, email: '<EMAIL>'))),

          // Override group translation providers
          groupTranslationSettingsProvider(sampleGroupId)
              .overrideWith((ref) => Future.value(sampleGroupSettings)),

          translationMetadataForUserProvider((
            message: sampleMessage,
            userId: sampleUserId,
            groupId: sampleGroupId,
          )).overrideWith((ref) => Future.value(sampleTranslationMetadata)),

          translatedTextForUserProvider((
            message: sampleMessage,
            userId: sampleUserId,
            groupId: sampleGroupId,
          )).overrideWith((ref) => Future.value('Bonjour, monde!')),

          participantLanguagePreferenceProvider((
            groupId: sampleGroupId,
            userId: sampleUserId,
          )).overrideWith((ref) => Future.value(
              sampleParticipantPreference.copyWith(showOriginalText: true))),
        ],
      );

      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: showOriginalContainer,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupTranslatedMessageBubble(
                message: sampleMessage,
                groupId: sampleGroupId,
                senderName: 'Sender Name',
                isMe: false,
              ),
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Should show translated text
      expect(find.text('Bonjour, monde!'), findsOneWidget);

      // Should show original text section
      expect(
          find.text('Original (English):', findRichText: true), findsOneWidget);
      expect(find.text('Hello, world!', findRichText: true),
          findsAtLeastNWidgets(1));
    });

    testWidgets('should call onLongPress when message is long-pressed',
        (WidgetTester tester) async {
      // Track if onLongPress was called
      bool onLongPressCalled = false;

      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupTranslatedMessageBubble(
                message: sampleMessage,
                groupId: sampleGroupId,
                senderName: 'Sender Name',
                isMe: false,
                onLongPress: () {
                  onLongPressCalled = true;
                },
              ),
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Long press the message bubble
      await tester.longPress(find.text('Bonjour, monde!'));

      // Verify onLongPress was called
      expect(onLongPressCalled, isTrue);
    });
  });
}
